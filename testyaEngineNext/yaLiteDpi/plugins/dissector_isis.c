#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "isis"

// ISIS Protocol Discriminator
#define ISIS_PROTOCOL_DISCRIMINATOR 0x83

// ISIS PDU Types
#define ISIS_PDU_L1_LAN_IIH         15  // Level-1 LAN IS-IS Hello PDU
#define ISIS_PDU_L2_LAN_IIH         16  // Level-2 LAN IS-IS Hello PDU
#define ISIS_PDU_P2P_IIH            17  // Point-to-Point IS-IS Hello PDU
#define ISIS_PDU_L1_LSP             18  // Level-1 Link State PDU
#define ISIS_PDU_L2_LSP             20  // Level-2 Link State PDU
#define ISIS_PDU_L1_CSNP            24  // Level-1 Complete Sequence Numbers PDU
#define ISIS_PDU_L2_CSNP            25  // Level-2 Complete Sequence Numbers PDU
#define ISIS_PDU_L1_PSNP            26  // Level-1 Partial Sequence Numbers PDU
#define ISIS_PDU_L2_PSNP            27  // Level-2 Partial Sequence Numbers PDU

// ISIS Version
#define ISIS_VERSION                1

static const char* isis_pdu_type_name(uint8_t pdu_type)
{
    switch (pdu_type) {
        case ISIS_PDU_L1_LAN_IIH: return "L1 LAN IIH";
        case ISIS_PDU_L2_LAN_IIH: return "L2 LAN IIH";
        case ISIS_PDU_P2P_IIH:    return "P2P IIH";
        case ISIS_PDU_L1_LSP:     return "L1 LSP";
        case ISIS_PDU_L2_LSP:     return "L2 LSP";
        case ISIS_PDU_L1_CSNP:    return "L1 CSNP";
        case ISIS_PDU_L2_CSNP:    return "L2 CSNP";
        case ISIS_PDU_L1_PSNP:    return "L1 PSNP";
        case ISIS_PDU_L2_PSNP:    return "L2 PSNP";
        default:                  return "Unknown";
    }
}

static
int isis_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum ISIS header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("ISIS: insufficient data length (%d bytes)\n", nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse ISIS common header
    uint8_t protocol_discriminator = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t length_indicator = nxt_mbuf_get_uint8(mbuf, 1);
    uint8_t version = nxt_mbuf_get_uint8(mbuf, 2);
    uint8_t id_length = nxt_mbuf_get_uint8(mbuf, 3);
    uint8_t pdu_type = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t version2 = nxt_mbuf_get_uint8(mbuf, 5);
    uint8_t reserved = nxt_mbuf_get_uint8(mbuf, 6);
    uint8_t max_area_addresses = nxt_mbuf_get_uint8(mbuf, 7);

    // Validate protocol discriminator
    if (protocol_discriminator != ISIS_PROTOCOL_DISCRIMINATOR) {
        printf("ISIS: invalid protocol discriminator 0x%02x (expected 0x%02x)\n", 
               protocol_discriminator, ISIS_PROTOCOL_DISCRIMINATOR);
        return -1;
    }

    // Validate version
    if (version != ISIS_VERSION) {
        printf("ISIS: unsupported version %d (expected %d)\n", version, ISIS_VERSION);
        return -1;
    }

    // Record common header fields
    precord_put(precord, "protocol_discriminator", uinteger, protocol_discriminator);
    precord_put(precord, "length_indicator", uinteger, length_indicator);
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "id_length", uinteger, id_length);
    precord_put(precord, "pdu_type", uinteger, pdu_type);
    precord_put(precord, "pdu_type_name", string, isis_pdu_type_name(pdu_type));
    precord_put(precord, "version2", uinteger, version2);
    precord_put(precord, "reserved", uinteger, reserved);
    precord_put(precord, "max_area_addresses", uinteger, max_area_addresses);

    printf("ISIS: PDU Type=%d (%s), Length=%d, ID Length=%d\n", 
           pdu_type, isis_pdu_type_name(pdu_type), length_indicator, id_length);

    int header_len = 8; // Common header length

    // Parse specific PDU types
    switch (pdu_type) {
        case ISIS_PDU_L1_LAN_IIH:
        case ISIS_PDU_L2_LAN_IIH:
        {
            // LAN IIH PDU - additional fields after common header
            if (nxt_mbuf_get_length(mbuf) < header_len + 19) {
                printf("ISIS: insufficient data for LAN IIH PDU\n");
                return -1;
            }
            
            uint8_t circuit_type = nxt_mbuf_get_uint8(mbuf, header_len);
            uint8_t source_id[6];
            for (int i = 0; i < 6; i++) {
                source_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 1 + i);
            }
            uint16_t holding_time = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 7);
            uint16_t pdu_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 9);
            uint8_t priority = nxt_mbuf_get_uint8(mbuf, header_len + 11);
            uint8_t lan_id[7];
            for (int i = 0; i < 7; i++) {
                lan_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 12 + i);
            }
            
            precord_put(precord, "circuit_type", uinteger, circuit_type);
            precord_put(precord, "source_id", bytes, source_id, 6);
            precord_put(precord, "holding_time", uinteger, holding_time);
            precord_put(precord, "pdu_length", uinteger, pdu_length);
            precord_put(precord, "priority", uinteger, priority);
            precord_put(precord, "lan_id", bytes, lan_id, 7);
            
            printf("ISIS: LAN IIH - Circuit Type=%d, Holding Time=%d, Priority=%d\n",
                   circuit_type, holding_time, priority);
            
            header_len += 19;
            break;
        }
        
        case ISIS_PDU_P2P_IIH:
        {
            // Point-to-Point IIH PDU
            if (nxt_mbuf_get_length(mbuf) < header_len + 12) {
                printf("ISIS: insufficient data for P2P IIH PDU\n");
                return -1;
            }
            
            uint8_t circuit_type = nxt_mbuf_get_uint8(mbuf, header_len);
            uint8_t source_id[6];
            for (int i = 0; i < 6; i++) {
                source_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 1 + i);
            }
            uint16_t holding_time = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 7);
            uint16_t pdu_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 9);
            uint8_t local_circuit_id = nxt_mbuf_get_uint8(mbuf, header_len + 11);
            
            precord_put(precord, "circuit_type", uinteger, circuit_type);
            precord_put(precord, "source_id", bytes, source_id, 6);
            precord_put(precord, "holding_time", uinteger, holding_time);
            precord_put(precord, "pdu_length", uinteger, pdu_length);
            precord_put(precord, "local_circuit_id", uinteger, local_circuit_id);
            
            printf("ISIS: P2P IIH - Circuit Type=%d, Holding Time=%d, Local Circuit ID=%d\n",
                   circuit_type, holding_time, local_circuit_id);
            
            header_len += 12;
            break;
        }
        
        case ISIS_PDU_L1_LSP:
        case ISIS_PDU_L2_LSP:
        {
            // Link State PDU
            if (nxt_mbuf_get_length(mbuf) < header_len + 19) {
                printf("ISIS: insufficient data for LSP PDU\n");
                return -1;
            }
            
            uint16_t pdu_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len);
            uint16_t remaining_lifetime = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 2);
            uint8_t lsp_id[8];
            for (int i = 0; i < 8; i++) {
                lsp_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 4 + i);
            }
            uint32_t sequence_number = nxt_mbuf_get_uint32_ntoh(mbuf, header_len + 12);
            uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 16);
            uint8_t att_error_delay_oload = nxt_mbuf_get_uint8(mbuf, header_len + 18);
            
            precord_put(precord, "pdu_length", uinteger, pdu_length);
            precord_put(precord, "remaining_lifetime", uinteger, remaining_lifetime);
            precord_put(precord, "lsp_id", bytes, lsp_id, 8);
            precord_put(precord, "sequence_number", uinteger, sequence_number);
            precord_put(precord, "checksum", uinteger, checksum);
            precord_put(precord, "att_error_delay_oload", uinteger, att_error_delay_oload);
            
            printf("ISIS: LSP - Lifetime=%d, Sequence=%u, Checksum=0x%04x\n",
                   remaining_lifetime, sequence_number, checksum);
            
            header_len += 19;
            break;
        }
        
        case ISIS_PDU_L1_CSNP:
        case ISIS_PDU_L2_CSNP:
        {
            // Complete Sequence Numbers PDU
            if (nxt_mbuf_get_length(mbuf) < header_len + 25) {
                printf("ISIS: insufficient data for CSNP PDU\n");
                return -1;
            }
            
            uint16_t pdu_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len);
            uint8_t source_id[7];
            for (int i = 0; i < 7; i++) {
                source_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 2 + i);
            }
            uint8_t start_lsp_id[8];
            for (int i = 0; i < 8; i++) {
                start_lsp_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 9 + i);
            }
            uint8_t end_lsp_id[8];
            for (int i = 0; i < 8; i++) {
                end_lsp_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 17 + i);
            }
            
            precord_put(precord, "pdu_length", uinteger, pdu_length);
            precord_put(precord, "source_id", bytes, source_id, 7);
            precord_put(precord, "start_lsp_id", bytes, start_lsp_id, 8);
            precord_put(precord, "end_lsp_id", bytes, end_lsp_id, 8);
            
            printf("ISIS: CSNP - PDU Length=%d\n", pdu_length);
            
            header_len += 25;
            break;
        }
        
        case ISIS_PDU_L1_PSNP:
        case ISIS_PDU_L2_PSNP:
        {
            // Partial Sequence Numbers PDU
            if (nxt_mbuf_get_length(mbuf) < header_len + 9) {
                printf("ISIS: insufficient data for PSNP PDU\n");
                return -1;
            }
            
            uint16_t pdu_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len);
            uint8_t source_id[7];
            for (int i = 0; i < 7; i++) {
                source_id[i] = nxt_mbuf_get_uint8(mbuf, header_len + 2 + i);
            }
            
            precord_put(precord, "pdu_length", uinteger, pdu_length);
            precord_put(precord, "source_id", bytes, source_id, 7);
            
            printf("ISIS: PSNP - PDU Length=%d\n", pdu_length);
            
            header_len += 9;
            break;
        }
        
        default:
            printf("ISIS: unknown PDU type %d\n", pdu_type);
            break;
    }

    return header_len;
}

static
int isis_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "intermediate system to intermediate system");
    
    // Common header fields
    pschema_register_field_ex(pschema, "protocol_discriminator", YA_FT_UINT8, "protocol discriminator", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "length_indicator", YA_FT_UINT8, "length indicator");
    pschema_register_field(pschema, "version", YA_FT_UINT8, "version");
    pschema_register_field(pschema, "id_length", YA_FT_UINT8, "ID length");
    pschema_register_field(pschema, "pdu_type", YA_FT_UINT8, "PDU type");
    pschema_register_field(pschema, "pdu_type_name", YA_FT_STRING, "PDU type name");
    pschema_register_field(pschema, "version2", YA_FT_UINT8, "version 2");
    pschema_register_field(pschema, "reserved", YA_FT_UINT8, "reserved");
    pschema_register_field(pschema, "max_area_addresses", YA_FT_UINT8, "maximum area addresses");
    
    // IIH specific fields
    pschema_register_field(pschema, "circuit_type", YA_FT_UINT8, "circuit type");
    pschema_register_field(pschema, "source_id", YA_FT_BYTES, "source ID");
    pschema_register_field(pschema, "holding_time", YA_FT_UINT16, "holding time");
    pschema_register_field(pschema, "pdu_length", YA_FT_UINT16, "PDU length");
    pschema_register_field(pschema, "priority", YA_FT_UINT8, "priority");
    pschema_register_field(pschema, "lan_id", YA_FT_BYTES, "LAN ID");
    pschema_register_field(pschema, "local_circuit_id", YA_FT_UINT8, "local circuit ID");
    
    // LSP specific fields
    pschema_register_field(pschema, "remaining_lifetime", YA_FT_UINT16, "remaining lifetime");
    pschema_register_field(pschema, "lsp_id", YA_FT_BYTES, "LSP ID");
    pschema_register_field(pschema, "sequence_number", YA_FT_UINT32, "sequence number");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "att_error_delay_oload", YA_FT_UINT8, "ATT/ERROR/DELAY/OLOAD flags", YA_DISPLAY_BASE_HEX);
    
    // CSNP/PSNP specific fields
    pschema_register_field(pschema, "start_lsp_id", YA_FT_BYTES, "start LSP ID");
    pschema_register_field(pschema, "end_lsp_id", YA_FT_BYTES, "end LSP ID");
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "isis",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = isis_schema_reg,
    .dissectFun   = isis_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // ISIS uses LLC with DSAP/SSAP = 0xFE (ISO CLNS)
        NXT_MNT_NUMBER("llc", 0xFE),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(isis)
{
    nxt_dissector_register(&gDissectorDef);
}
