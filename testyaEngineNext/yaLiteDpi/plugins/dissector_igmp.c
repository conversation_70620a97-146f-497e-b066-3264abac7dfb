#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "igmp"

static
int igmp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum IGMP header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("IGMP: insufficient data length (%d bytes, need at least 8)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse IGMP header
    uint8_t type = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t max_resp_time = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint32_t group_address = nxt_mbuf_get_uint32_ntoh(mbuf, 4);

    // Record IGMP fields
    precord_put(precord, "type", uinteger, type);
    precord_put(precord, "max_resp_time", uinteger, max_resp_time);
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "group_address", uinteger, group_address);

    printf("IGMP: Type=0x%02x, Max Resp Time=%d, Group=0x%08x\n", 
           type, max_resp_time, group_address);

    return 8; // Basic IGMP header length
}

static
int igmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internet group management protocol");
    pschema_register_field_ex(pschema, "type", YA_FT_UINT8, "message type", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "max_resp_time", YA_FT_UINT8, "maximum response time");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "header checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "group_address", YA_FT_UINT32, "group address", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "igmp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = igmp_schema_reg,
    .dissectFun   = igmp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(igmp)
{
    nxt_dissector_register(&gDissectorDef);
}
