#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "rsvp"

static
int rsvp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum RSVP header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("RSVP: insufficient data length (%d bytes, need at least 8)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse RSVP common header
    uint8_t version_flags = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t msg_type = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t reserved _U_ = nxt_mbuf_get_uint8(mbuf, 5);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 6);

    uint8_t version = (version_flags >> 4) & 0x0F;
    uint8_t flags = version_flags & 0x0F;

    // Record RSVP fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "flags", uinteger, flags);
    precord_put(precord, "msg_type", uinteger, msg_type);
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "ttl", uinteger, ttl);
    precord_put(precord, "length", uinteger, length);

    printf("RSVP: Version=%d, Type=%d, TTL=%d, Length=%d\n", 
           version, msg_type, ttl, length);

    return 8; // Basic RSVP header length
}

static
int rsvp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "resource reservation protocol");
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field_ex(pschema, "flags", YA_FT_UINT8, "header flags", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "msg_type", YA_FT_UINT8, "message type", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "header checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rsvp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = rsvp_schema_reg,
    .dissectFun   = rsvp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rsvp)
{
    nxt_dissector_register(&gDissectorDef);
}
