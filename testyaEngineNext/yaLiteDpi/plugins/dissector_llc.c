#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "llc"

// LLC Control field types
#define LLC_CONTROL_I_FRAME     0x00  // Information frame (bit 0 = 0)
#define LLC_CONTROL_S_FRAME     0x01  // Supervisory frame (bits 1-0 = 01)
#define LLC_CONTROL_U_FRAME     0x03  // Unnumbered frame (bits 1-0 = 11)

// LLC DSAP/SSAP values
#define LLC_SAP_NULL            0x00
#define LLC_SAP_LLC_SUBLAYER    0x02
#define LLC_SAP_SNA_PATH_CTRL   0x04
#define LLC_SAP_IP              0x06
#define LLC_SAP_SNA1            0x08
#define LLC_SAP_SNA2            0x0C
#define LLC_SAP_PROWAY_NM       0x0E
#define LLC_SAP_NETWARE         0xE0
#define LLC_SAP_PROWAY_ACTIVE   0x8E
#define LLC_SAP_ARP             0x98
#define LLC_SAP_SNAP            0xAA
#define LLC_SAP_VINES1          0xBA
#define LLC_SAP_VINES2          0xBC
#define LLC_SAP_NETBIOS         0xF0
#define LLC_SAP_IBMSNA          0xF4
#define LLC_SAP_IBMNET1         0xF8
#define LLC_SAP_RPL             0xFC
#define LLC_SAP_UB              0xFA
#define LLC_SAP_XNS             0x80
#define LLC_SAP_NESTAR          0x86
#define LLC_SAP_BACNET          0x82
#define LLC_SAP_LANMAN          0xF0

static
int llc_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check if we have at least 3 bytes for LLC header (DSAP + SSAP + Control)
    if (nxt_mbuf_get_length(mbuf) < 3) {
        printf("LLC: insufficient data length (%d bytes)\n", nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse LLC header fields
    uint8_t dsap = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t ssap = nxt_mbuf_get_uint8(mbuf, 1);
    uint8_t control = nxt_mbuf_get_uint8(mbuf, 2);

    // Extract individual/group bit from DSAP (bit 0)
    uint8_t dsap_ig = dsap & 0x01;
    uint8_t dsap_addr = dsap & 0xFE;

    // Extract command/response bit from SSAP (bit 0)
    uint8_t ssap_cr = ssap & 0x01;
    uint8_t ssap_addr = ssap & 0xFE;

    // Record LLC fields
    precord_put(precord, "dsap", uinteger, dsap);
    precord_put(precord, "ssap", uinteger, ssap);
    precord_put(precord, "dsap_ig", uinteger, dsap_ig);
    precord_put(precord, "dsap_addr", uinteger, dsap_addr);
    precord_put(precord, "ssap_cr", uinteger, ssap_cr);
    precord_put(precord, "ssap_addr", uinteger, ssap_addr);

    int header_len = 3; // Default header length

    // Determine frame type and control field format
    if ((control & 0x01) == 0) {
        // Information frame (I-frame): bit 0 = 0
        if (nxt_mbuf_get_length(mbuf) < 4) {
            printf("LLC: insufficient data for I-frame control field\n");
            return -1;
        }
        uint8_t control2 = nxt_mbuf_get_uint8(mbuf, 3);
        uint16_t full_control = (control << 8) | control2;
        
        uint8_t ns = (control >> 1) & 0x7F;  // Send sequence number
        uint8_t nr = (control2 >> 1) & 0x7F; // Receive sequence number
        uint8_t pf = control2 & 0x01;        // Poll/Final bit
        
        precord_put(precord, "control", uinteger, full_control);
        precord_put(precord, "frame_type", string, "I");
        precord_put(precord, "ns", uinteger, ns);
        precord_put(precord, "nr", uinteger, nr);
        precord_put(precord, "pf", uinteger, pf);
        header_len = 4;
    } else if ((control & 0x03) == 0x01) {
        // Supervisory frame (S-frame): bits 1-0 = 01
        if (nxt_mbuf_get_length(mbuf) < 4) {
            printf("LLC: insufficient data for S-frame control field\n");
            return -1;
        }
        uint8_t control2 = nxt_mbuf_get_uint8(mbuf, 3);
        uint16_t full_control = (control << 8) | control2;
        
        uint8_t ss = (control >> 2) & 0x03;  // Supervisory function
        uint8_t nr = (control2 >> 1) & 0x7F; // Receive sequence number
        uint8_t pf = control2 & 0x01;        // Poll/Final bit
        
        precord_put(precord, "control", uinteger, full_control);
        precord_put(precord, "frame_type", string, "S");
        precord_put(precord, "ss", uinteger, ss);
        precord_put(precord, "nr", uinteger, nr);
        precord_put(precord, "pf", uinteger, pf);
        header_len = 4;
    } else {
        // Unnumbered frame (U-frame): bits 1-0 = 11
        uint8_t modifier = (control >> 2) & 0x1F; // Modifier function
        uint8_t pf = (control >> 4) & 0x01;       // Poll/Final bit
        
        precord_put(precord, "control", uinteger, control);
        precord_put(precord, "frame_type", string, "U");
        precord_put(precord, "modifier", uinteger, modifier);
        precord_put(precord, "pf", uinteger, pf);
        header_len = 3;
    }

    printf("LLC: DSAP=0x%02x SSAP=0x%02x Control=0x%02x\n", dsap, ssap, control);

    // Handle SNAP extension
    if (dsap == LLC_SAP_SNAP && ssap == LLC_SAP_SNAP) {
        if (nxt_mbuf_get_length(mbuf) < header_len + 5) {
            printf("LLC: insufficient data for SNAP header\n");
            return -1;
        }
        
        // SNAP header: 3 bytes OUI + 2 bytes Type
        uint32_t oui = (nxt_mbuf_get_uint8(mbuf, header_len) << 16) |
                       (nxt_mbuf_get_uint8(mbuf, header_len + 1) << 8) |
                       nxt_mbuf_get_uint8(mbuf, header_len + 2);
        uint16_t snap_type = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 3);
        
        precord_put(precord, "snap_oui", uinteger, oui);
        precord_put(precord, "snap_type", uinteger, snap_type);
        
        printf("LLC: SNAP OUI=0x%06x Type=0x%04x\n", oui, snap_type);
        
        // For SNAP, use the SNAP type for handoff
        nxt_handoff_set_key_of_number(engine, snap_type);
        header_len += 5;
    } else {
        // For non-SNAP, use DSAP for handoff
        nxt_handoff_set_key_of_number(engine, dsap_addr);
    }

    return header_len;
}

static
int llc_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "logical link control");
    pschema_register_field_ex(pschema, "dsap", YA_FT_UINT8, "destination service access point", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "ssap", YA_FT_UINT8, "source service access point", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "dsap_ig", YA_FT_UINT8, "dsap individual/group bit");
    pschema_register_field_ex(pschema, "dsap_addr", YA_FT_UINT8, "dsap address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "ssap_cr", YA_FT_UINT8, "ssap command/response bit");
    pschema_register_field_ex(pschema, "ssap_addr", YA_FT_UINT8, "ssap address", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "control", YA_FT_UINT16, "control field", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "frame_type", YA_FT_STRING, "frame type (I/S/U)");
    pschema_register_field(pschema, "ns", YA_FT_UINT8, "send sequence number");
    pschema_register_field(pschema, "nr", YA_FT_UINT8, "receive sequence number");
    pschema_register_field(pschema, "pf", YA_FT_UINT8, "poll/final bit");
    pschema_register_field(pschema, "ss", YA_FT_UINT8, "supervisory function");
    pschema_register_field(pschema, "modifier", YA_FT_UINT8, "unnumbered modifier");
    pschema_register_field_ex(pschema, "snap_oui", YA_FT_UINT32, "SNAP organizationally unique identifier", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "snap_type", YA_FT_UINT16, "SNAP type", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "llc",
    .type         = NXT_DISSECTOR_TYPE_LINK,
    .schemaRegFun = llc_schema_reg,
    .dissectFun   = llc_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // LLC is typically used in 802.3 frames when length <= 1500
        // It's mounted after ethernet when the type/length field indicates 802.3
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(llc)
{
    nxt_dissector_register(&gDissectorDef);
    
    // Register handoff rules for common protocols over LLC
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "llc", LLC_SAP_IP, "ipv4");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "llc", LLC_SAP_ARP, "arp");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "llc", LLC_SAP_NETBIOS, "netbios");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "llc", LLC_SAP_XNS, "xns");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "llc", LLC_SAP_BACNET, "bacnet");
    
    // For SNAP frames, we'll handle handoff based on SNAP type in the dissector
}
