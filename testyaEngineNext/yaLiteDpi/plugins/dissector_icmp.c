#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "icmp"

// ICMP Message Types (RFC 792 and others)
#define ICMP_TYPE_ECHO_REPLY            0   // Echo Reply
#define ICMP_TYPE_DEST_UNREACHABLE      3   // Destination Unreachable
#define ICMP_TYPE_SOURCE_QUENCH         4   // Source Quench (deprecated)
#define ICMP_TYPE_REDIRECT              5   // Redirect
#define ICMP_TYPE_ECHO_REQUEST          8   // Echo Request
#define ICMP_TYPE_ROUTER_ADVERTISEMENT  9   // Router Advertisement
#define ICMP_TYPE_ROUTER_SOLICITATION   10  // Router Solicitation
#define ICMP_TYPE_TIME_EXCEEDED         11  // Time Exceeded
#define ICMP_TYPE_PARAMETER_PROBLEM     12  // Parameter Problem
#define ICMP_TYPE_TIMESTAMP_REQUEST     13  // Timestamp Request
#define ICMP_TYPE_TIMESTAMP_REPLY       14  // Timestamp Reply
#define ICMP_TYPE_INFO_REQUEST          15  // Information Request (deprecated)
#define ICMP_TYPE_INFO_REPLY            16  // Information Reply (deprecated)
#define ICMP_TYPE_ADDRESS_MASK_REQUEST  17  // Address Mask Request
#define ICMP_TYPE_ADDRESS_MASK_REPLY    18  // Address Mask Reply

// ICMP Destination Unreachable Codes
#define ICMP_CODE_NET_UNREACHABLE       0   // Network Unreachable
#define ICMP_CODE_HOST_UNREACHABLE      1   // Host Unreachable
#define ICMP_CODE_PROTOCOL_UNREACHABLE  2   // Protocol Unreachable
#define ICMP_CODE_PORT_UNREACHABLE      3   // Port Unreachable
#define ICMP_CODE_FRAG_NEEDED           4   // Fragmentation needed but DF set
#define ICMP_CODE_SOURCE_ROUTE_FAILED   5   // Source route failed

// ICMP Redirect Codes
#define ICMP_CODE_REDIRECT_NET          0   // Redirect for Network
#define ICMP_CODE_REDIRECT_HOST         1   // Redirect for Host
#define ICMP_CODE_REDIRECT_TOS_NET      2   // Redirect for Type of Service and Network
#define ICMP_CODE_REDIRECT_TOS_HOST     3   // Redirect for Type of Service and Host

// ICMP Time Exceeded Codes
#define ICMP_CODE_TTL_EXCEEDED          0   // Time to Live exceeded in Transit
#define ICMP_CODE_FRAG_REASSEMBLY       1   // Fragment Reassembly Time Exceeded

static const char* icmp_type_name(uint8_t type)
{
    switch (type) {
        case ICMP_TYPE_ECHO_REPLY:            return "Echo Reply";
        case ICMP_TYPE_DEST_UNREACHABLE:      return "Destination Unreachable";
        case ICMP_TYPE_SOURCE_QUENCH:         return "Source Quench";
        case ICMP_TYPE_REDIRECT:              return "Redirect";
        case ICMP_TYPE_ECHO_REQUEST:          return "Echo Request";
        case ICMP_TYPE_ROUTER_ADVERTISEMENT:  return "Router Advertisement";
        case ICMP_TYPE_ROUTER_SOLICITATION:   return "Router Solicitation";
        case ICMP_TYPE_TIME_EXCEEDED:         return "Time Exceeded";
        case ICMP_TYPE_PARAMETER_PROBLEM:     return "Parameter Problem";
        case ICMP_TYPE_TIMESTAMP_REQUEST:     return "Timestamp Request";
        case ICMP_TYPE_TIMESTAMP_REPLY:       return "Timestamp Reply";
        case ICMP_TYPE_INFO_REQUEST:          return "Information Request";
        case ICMP_TYPE_INFO_REPLY:            return "Information Reply";
        case ICMP_TYPE_ADDRESS_MASK_REQUEST:  return "Address Mask Request";
        case ICMP_TYPE_ADDRESS_MASK_REPLY:    return "Address Mask Reply";
        default:                              return "Unknown";
    }
}

static const char* icmp_code_name(uint8_t type, uint8_t code)
{
    switch (type) {
        case ICMP_TYPE_DEST_UNREACHABLE:
            switch (code) {
                case ICMP_CODE_NET_UNREACHABLE:       return "Network Unreachable";
                case ICMP_CODE_HOST_UNREACHABLE:      return "Host Unreachable";
                case ICMP_CODE_PROTOCOL_UNREACHABLE:  return "Protocol Unreachable";
                case ICMP_CODE_PORT_UNREACHABLE:      return "Port Unreachable";
                case ICMP_CODE_FRAG_NEEDED:           return "Fragmentation needed but DF set";
                case ICMP_CODE_SOURCE_ROUTE_FAILED:   return "Source route failed";
                default:                              return "Unknown";
            }
        case ICMP_TYPE_REDIRECT:
            switch (code) {
                case ICMP_CODE_REDIRECT_NET:          return "Redirect for Network";
                case ICMP_CODE_REDIRECT_HOST:         return "Redirect for Host";
                case ICMP_CODE_REDIRECT_TOS_NET:      return "Redirect for TOS and Network";
                case ICMP_CODE_REDIRECT_TOS_HOST:     return "Redirect for TOS and Host";
                default:                              return "Unknown";
            }
        case ICMP_TYPE_TIME_EXCEEDED:
            switch (code) {
                case ICMP_CODE_TTL_EXCEEDED:          return "TTL exceeded in transit";
                case ICMP_CODE_FRAG_REASSEMBLY:       return "Fragment reassembly time exceeded";
                default:                              return "Unknown";
            }
        default:
            return (code == 0) ? "Normal" : "Unknown";
    }
}

static
int icmp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum ICMP header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("ICMP: insufficient data length (%d bytes, need at least 8)\n",
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse ICMP header
    uint8_t type = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t code = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint32_t rest_of_header = nxt_mbuf_get_uint32_ntoh(mbuf, 4);

    // Record common ICMP fields
    precord_put(precord, "type", uinteger, type);
    precord_put(precord, "code", uinteger, code);
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "type_name", string, icmp_type_name(type));
    precord_put(precord, "code_name", string, icmp_code_name(type, code));

    printf("ICMP: Type=%d (%s), Code=%d (%s), Checksum=0x%04x\n", 
           type, icmp_type_name(type), code, icmp_code_name(type, code), checksum);

    // Parse type-specific fields
    switch (type) {
        case ICMP_TYPE_ECHO_REQUEST:
        case ICMP_TYPE_ECHO_REPLY:
        {
            // Echo Request/Reply: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Echo - Identifier=%d, Sequence=%d\n", identifier, sequence);
            break;
        }

        case ICMP_TYPE_DEST_UNREACHABLE:
        case ICMP_TYPE_SOURCE_QUENCH:
        case ICMP_TYPE_TIME_EXCEEDED:
        case ICMP_TYPE_PARAMETER_PROBLEM:
        {
            // These types typically have unused field (should be 0) or specific data
            precord_put(precord, "unused", uinteger, rest_of_header);
            
            if (type == ICMP_TYPE_PARAMETER_PROBLEM) {
                uint8_t pointer = (rest_of_header >> 24) & 0xFF;
                precord_put(precord, "pointer", uinteger, pointer);
                printf("ICMP: Parameter Problem - Pointer=%d\n", pointer);
            }
            break;
        }

        case ICMP_TYPE_REDIRECT:
        {
            // Redirect: Gateway Internet Address (4 bytes)
            uint32_t gateway_address = rest_of_header;
            precord_put(precord, "gateway_address", uinteger, gateway_address);
            
            printf("ICMP: Redirect - Gateway=0x%08x\n", gateway_address);
            break;
        }

        case ICMP_TYPE_TIMESTAMP_REQUEST:
        case ICMP_TYPE_TIMESTAMP_REPLY:
        {
            // Timestamp: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Timestamp - Identifier=%d, Sequence=%d\n", identifier, sequence);
            
            // Parse timestamp fields if present
            if (nxt_mbuf_get_length(mbuf) >= 20) {
                uint32_t originate_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                uint32_t receive_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 12);
                uint32_t transmit_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 16);
                
                precord_put(precord, "originate_timestamp", uinteger, originate_timestamp);
                precord_put(precord, "receive_timestamp", uinteger, receive_timestamp);
                precord_put(precord, "transmit_timestamp", uinteger, transmit_timestamp);
                
                printf("ICMP: Timestamps - Orig=%u, Recv=%u, Trans=%u\n",
                       originate_timestamp, receive_timestamp, transmit_timestamp);
            }
            break;
        }

        case ICMP_TYPE_ADDRESS_MASK_REQUEST:
        case ICMP_TYPE_ADDRESS_MASK_REPLY:
        {
            // Address Mask: Identifier (2 bytes) + Sequence Number (2 bytes)
            uint16_t identifier = (rest_of_header >> 16) & 0xFFFF;
            uint16_t sequence = rest_of_header & 0xFFFF;
            
            precord_put(precord, "identifier", uinteger, identifier);
            precord_put(precord, "sequence", uinteger, sequence);
            
            printf("ICMP: Address Mask - Identifier=%d, Sequence=%d\n", identifier, sequence);
            
            // Parse address mask if present (for reply)
            if (type == ICMP_TYPE_ADDRESS_MASK_REPLY && nxt_mbuf_get_length(mbuf) >= 12) {
                uint32_t address_mask = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                precord_put(precord, "address_mask", uinteger, address_mask);
                printf("ICMP: Address Mask = 0x%08x\n", address_mask);
            }
            break;
        }

        default:
        {
            // Unknown type - record the rest of header as generic data
            precord_put(precord, "rest_of_header", uinteger, rest_of_header);
            break;
        }
    }

    return 8; // Basic ICMP header length
}

static
int icmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internet control message protocol");
    
    // ICMP header fields
    pschema_register_field(pschema, "type", YA_FT_UINT8, "ICMP type");
    pschema_register_field(pschema, "code", YA_FT_UINT8, "ICMP code");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "type_name", YA_FT_STRING, "ICMP type name");
    pschema_register_field(pschema, "code_name", YA_FT_STRING, "ICMP code name");
    
    // Type-specific fields
    pschema_register_field(pschema, "identifier", YA_FT_UINT16, "identifier");
    pschema_register_field(pschema, "sequence", YA_FT_UINT16, "sequence number");
    pschema_register_field(pschema, "unused", YA_FT_UINT32, "unused field");
    pschema_register_field(pschema, "pointer", YA_FT_UINT8, "parameter problem pointer");
    pschema_register_field_ex(pschema, "gateway_address", YA_FT_UINT32, "gateway address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "originate_timestamp", YA_FT_UINT32, "originate timestamp");
    pschema_register_field(pschema, "receive_timestamp", YA_FT_UINT32, "receive timestamp");
    pschema_register_field(pschema, "transmit_timestamp", YA_FT_UINT32, "transmit timestamp");
    pschema_register_field_ex(pschema, "address_mask", YA_FT_UINT32, "address mask", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "rest_of_header", YA_FT_UINT32, "rest of header", YA_DISPLAY_BASE_HEX);
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "icmp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = icmp_schema_reg,
    .dissectFun   = icmp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // ICMP is carried directly in IP packets with protocol number 1
        NXT_MNT_NUMBER("ipv4", 1),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(icmp)
{
    nxt_dissector_register(&gDissectorDef);
}
